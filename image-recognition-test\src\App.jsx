import React, { useState, useEffect } from 'react';
import { Layout, Typography, Space, Card, Row, Col, Button, Modal, Input, Form, message, Divider, Alert, Tabs } from 'antd';
import { SettingOutlined, InfoCircleOutlined, GithubOutlined, BarcodeOutlined } from '@ant-design/icons';
import ImageUpload from './components/ImageUpload';
import LabelRenderer from './components/LabelRenderer';
import BarcodeTest from './components/BarcodeTest';
import textInApi from './services/textinApi';
import dataConverter from './services/dataConverter';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Text, Paragraph } = Typography;

function App() {
  const [loading, setLoading] = useState(false);
  const [recognitionResult, setRecognitionResult] = useState(null);
  const [convertedData, setConvertedData] = useState(null);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [credentials, setCredentials] = useState({
    appId: '',
    secretCode: ''
  });

  // 从localStorage加载凭据
  useEffect(() => {
    const savedCredentials = localStorage.getItem('textin-credentials');
    if (savedCredentials) {
      try {
        const parsed = JSON.parse(savedCredentials);
        setCredentials(parsed);
        textInApi.setCredentials(parsed.appId, parsed.secretCode);
      } catch (error) {
        console.error('加载凭据失败:', error);
      }
    }
  }, []);

  const handleUploadSuccess = (result, file) => {
    console.log('识别成功:', result);
    setRecognitionResult(result);

    try {
      // 转换数据格式
      const converted = dataConverter.convertToXPrinter(result);
      setConvertedData(converted);
      console.log('转换后的数据:', converted);
    } catch (error) {
      console.error('数据转换失败:', error);
      message.error(`数据转换失败: ${error.message}`);
    }
  };

  const handleUploadError = (error) => {
    console.error('识别失败:', error);
    setRecognitionResult(null);
    setConvertedData(null);
  };

  const handleDataChange = (newData) => {
    console.log('数据已更新:', newData);
    setConvertedData(newData);
  };

  const handleSaveCredentials = (values) => {
    try {
      setCredentials(values);
      textInApi.setCredentials(values.appId, values.secretCode);
      localStorage.setItem('textin-credentials', JSON.stringify(values));
      setSettingsVisible(false);
      message.success('API凭据保存成功');
    } catch (error) {
      console.error('保存凭据失败:', error);
      message.error('保存凭据失败');
    }
  };

  const downloadJson = (data, filename) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        background: '#fff',
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            图像识别测试平台
          </Title>
          <Text type="secondary" style={{ marginLeft: 16 }}>
            基于 TextIn API 的文档解析与标签生成
          </Text>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            API设置
          </Button>
          <Button
            icon={<InfoCircleOutlined />}
            type="link"
            href="https://www.textin.com/document/pdf_to_markdown"
            target="_blank"
          >
            API文档
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: 1400, margin: '0 auto' }}>
          {/* API状态提示 */}
          {!credentials.appId || !credentials.secretCode ? (
            <Alert
              message="请先配置 TextIn API 凭据"
              description="点击右上角的 API设置 按钮配置您的 TextIn API 凭据后才能使用识别功能。"
              type="warning"
              showIcon
              style={{ marginBottom: 24 }}
              action={
                <Button size="small" onClick={() => setSettingsVisible(true)}>
                  立即配置
                </Button>
              }
            />
          ) : (
            <Alert
              message="API 凭据已配置"
              description="您可以开始上传文件进行识别了。"
              type="success"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          <Tabs
            defaultActiveKey="recognition"
            items={[
              {
                key: 'recognition',
                label: '图像识别',
                children: (
                  <Row gutter={[24, 24]}>
                    {/* 左侧：文件上传 */}
                    <Col xs={24} lg={8}>
                      <ImageUpload
                        onUploadSuccess={handleUploadSuccess}
                        onUploadError={handleUploadError}
                        loading={loading}
                        setLoading={setLoading}
                      />
                    </Col>

                    {/* 右侧：结果展示 */}
                    <Col xs={24} lg={16}>
                      {convertedData ? (
                        <LabelRenderer
                          data={convertedData}
                          onDataChange={handleDataChange}
                        />
                      ) : (
                        <Card title="标签预览">
                          <div style={{
                            textAlign: 'center',
                            padding: '60px 20px',
                            color: '#999'
                          }}>
                            <InfoCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                            <Paragraph>
                              请上传图片或文档文件开始识别
                            </Paragraph>
                            <Text type="secondary">
                              支持 PNG、JPG、PDF 等格式，识别后将显示标签预览
                            </Text>
                          </div>
                        </Card>
                      )}
                    </Col>
                  </Row>
                )
              },
              {
                key: 'barcode-test',
                label: (
                  <span>
                    <BarcodeOutlined />
                    条形码测试
                  </span>
                ),
                children: <BarcodeTest />
              }
            ]}
          />

          {/* 数据下载区域 */}
          {(recognitionResult || convertedData) && (
            <>
              <Divider />
              <Card title="数据下载" style={{ marginTop: 24 }}>
                <Space wrap>
                  {recognitionResult && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(recognitionResult, 'textin-result.json')}
                    >
                      下载 TextIn 原始数据
                    </Button>
                  )}
                  {convertedData && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(convertedData, 'xprinter-data.json')}
                    >
                      下载 XPrinter 格式数据
                    </Button>
                  )}
                </Space>
              </Card>
            </>
          )}
        </div>
      </Content>

      <Footer style={{ textAlign: 'center', background: '#fafafa' }}>
        <Space split={<Divider type="vertical" />}>
          <Text type="secondary">
            图像识别测试平台 ©2024
          </Text>
          <Text type="secondary">
            基于 TextIn API 构建
          </Text>
          <a href="https://github.com" target="_blank" rel="noopener noreferrer">
            <GithubOutlined /> GitHub
          </a>
        </Space>
      </Footer>

      {/* API设置模态框 */}
      <Modal
        title="TextIn API 设置"
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          layout="vertical"
          initialValues={credentials}
          onFinish={handleSaveCredentials}
        >
          <Alert
            message="获取 API 凭据"
            description={
              <div>
                请登录 <a href="https://www.textin.com" target="_blank" rel="noopener noreferrer">TextIn 官网</a>，
                前往 工作台-账号设置-开发者信息 查看您的 API 凭据。
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label="App ID"
            name="appId"
            rules={[{ required: true, message: '请输入 App ID' }]}
          >
            <Input placeholder="请输入您的 TextIn App ID" />
          </Form.Item>

          <Form.Item
            label="Secret Code"
            name="secretCode"
            rules={[{ required: true, message: '请输入 Secret Code' }]}
          >
            <Input.Password placeholder="请输入您的 TextIn Secret Code" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setSettingsVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
}

export default App;
