import React, { useEffect, useRef } from 'react';
import JsBarcode from 'jsbarcode';

// XPrinter条形码类型到JsBarcode格式的映射
const BARCODE_FORMAT_MAP = {
  '1': 'CODE128',
  '2': 'CODE39',
  '3': 'CODE93',
  '4': 'EAN13',
  '5': 'EAN8',
  '6': 'ITF',
  '7': 'ITF14',
  '8': 'UPC',
  '9': 'UPC',
  '10': 'codabar',
  '11': 'CODE128', // CHINA_POST 使用 CODE128 替代
  '12': 'ITF',     // MATRIX_25 使用 ITF 替代
  '13': 'ITF'      // INDUSTRIAL_25 使用 ITF 替代
};

const BarcodeRenderer = ({
  value = '123456789',
  format = 'CODE128',
  width = 2,
  height = 100,
  displayValue = true,
  fontSize = 20,
  textAlign = 'center',
  textPosition = 'bottom',
  background = '#ffffff',
  lineColor = '#000000',
  style = {}
}) => {
  const canvasRef = useRef(null);

  // 转换条形码格式
  const getJsBarcodeFormat = (formatInput) => {
    // 如果是数字字符串，从映射表获取
    if (typeof formatInput === 'string' && /^\d+$/.test(formatInput)) {
      return BARCODE_FORMAT_MAP[formatInput] || 'CODE128';
    }
    // 如果已经是字符串格式，直接返回
    return formatInput || 'CODE128';
  };

  // 验证条形码数据是否符合格式要求
  const validateBarcodeData = (value, format) => {
    const barcodeFormat = getJsBarcodeFormat(format);

    switch (barcodeFormat) {
      case 'EAN13':
        // EAN13需要13位数字
        return /^\d{13}$/.test(value);
      case 'EAN8':
        // EAN8需要8位数字
        return /^\d{8}$/.test(value);
      case 'UPC':
        // UPC-A需要12位数字
        return /^\d{12}$/.test(value);
      case 'CODE39':
        // CODE39支持数字、大写字母和特殊字符
        return /^[0-9A-Z\-\.\s\$\/\+%]+$/.test(value);
      case 'ITF':
      case 'ITF14':
        // ITF需要偶数位数字
        return /^\d+$/.test(value) && value.length % 2 === 0;
      default:
        // 其他格式相对宽松
        return value && value.length > 0;
    }
  };

  useEffect(() => {
    if (canvasRef.current && value) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      // 计算合适的Canvas尺寸
      const canvasWidth = Math.max(200, width * 100);
      const canvasHeight = Math.max(60, height + (displayValue ? fontSize + 10 : 0));

      // 设置Canvas的实际尺寸
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      // 清除之前的内容
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 获取正确的条形码格式
      const barcodeFormat = getJsBarcodeFormat(format);
      console.log(`条形码渲染: 输入格式=${format}, 转换格式=${barcodeFormat}, 内容=${value}, Canvas尺寸=${canvasWidth}x${canvasHeight}`);

      // 验证数据格式
      if (!validateBarcodeData(value, format)) {
        console.warn(`条形码数据格式不正确: ${value} (${barcodeFormat})`);
        ctx.fillStyle = '#ff6600';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`数据格式不正确`, canvas.width / 2, canvas.height / 2 - 10);
        ctx.fillText(`${barcodeFormat}: ${value}`, canvas.width / 2, canvas.height / 2 + 10);
        return;
      }

      try {
        // 生成条码
        JsBarcode(canvas, value, {
          format: barcodeFormat,
          width: width,
          height: height,
          displayValue: displayValue,
          fontSize: fontSize,
          textAlign: textAlign,
          textPosition: textPosition,
          background: background,
          lineColor: lineColor,
          margin: 0
        });
      } catch (error) {
        console.error('条码生成失败:', error);

        // 如果生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`条码生成失败`, canvas.width / 2, canvas.height / 2 - 10);
        ctx.fillText(`${error.message}`, canvas.width / 2, canvas.height / 2 + 10);
      }
    }
  }, [value, format, width, height, displayValue, fontSize, textAlign, textPosition, background, lineColor]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        objectFit: 'contain',
        display: 'block',
        backgroundColor: background,
        ...style
      }}
    />
  );
};

export default BarcodeRenderer;
