import React, { useState } from 'react';
import { Card, Space, Input, Select, Button, Alert, Typography } from 'antd';
import Barcode<PERSON>enderer from './BarcodeRenderer';

const { Option } = Select;
const { Text } = Typography;

const BarcodeTest = () => {
  const [value, setValue] = useState('123456789');
  const [format, setFormat] = useState('1'); // XPrinter格式

  // XPrinter条形码类型选项
  const barcodeTypes = [
    { value: '1', label: 'CODE_128' },
    { value: '2', label: 'CODE_39' },
    { value: '3', label: 'CODE_93' },
    { value: '4', label: 'EAN_13' },
    { value: '5', label: 'EAN_8' },
    { value: '6', label: 'ITF_25' },
    { value: '7', label: 'ITF_14' },
    { value: '8', label: 'UPC_A' },
    { value: '9', label: 'UPC_E' },
    { value: '10', label: 'CODABAR' }
  ];

  const testCases = [
    { value: '123456789', format: '1', name: 'CODE_128 测试' },
    { value: '1234567890128', format: '4', name: 'EAN_13 测试 (简单)' },
    { value: '*************', format: '4', name: 'EAN_13 测试 (标准)' },
    { value: '49012345', format: '5', name: 'EAN_8 测试' },
    { value: 'ABC123', format: '2', name: 'CODE_39 测试' },
    { value: '123456789012', format: '8', name: 'UPC_A 测试' },
    { value: '1234567890', format: '6', name: 'ITF 测试' }
  ];

  // 格式要求说明
  const getFormatRequirement = (formatValue) => {
    switch (formatValue) {
      case '4': return 'EAN_13: 需要13位数字';
      case '5': return 'EAN_8: 需要8位数字';
      case '8': return 'UPC_A: 需要12位数字';
      case '9': return 'UPC_E: 需要6-8位数字';
      case '6': return 'ITF: 需要偶数位数字';
      case '7': return 'ITF_14: 需要14位数字';
      case '2': return 'CODE_39: 支持数字、大写字母和特殊字符';
      case '10': return 'CODABAR: 支持数字和特殊字符';
      default: return 'CODE_128: 支持所有ASCII字符';
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="条形码渲染测试" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="条形码格式说明"
            description="不同的条形码类型对数据格式有特定要求，请根据选择的类型输入正确格式的数据。"
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <div>
            <label>条形码内容: </label>
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="输入条形码内容"
              style={{ width: '300px', marginLeft: '10px' }}
            />
          </div>

          <div>
            <label>条形码类型: </label>
            <Select
              value={format}
              onChange={setFormat}
              style={{ width: '200px', marginLeft: '10px' }}
            >
              {barcodeTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </div>

          <div style={{ marginLeft: '90px' }}>
            <Text type="secondary">{getFormatRequirement(format)}</Text>
          </div>

          <div style={{ 
            border: '1px solid #d9d9d9', 
            padding: '20px', 
            backgroundColor: '#f5f5f5',
            minHeight: '150px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <BarcodeRenderer
              value={value}
              format={format}
              width={2}
              height={100}
              displayValue={true}
              fontSize={16}
              style={{
                maxWidth: '400px',
                maxHeight: '120px'
              }}
            />
          </div>
        </Space>
      </Card>

      <Card title="预设测试用例">
        <Space direction="vertical" style={{ width: '100%' }}>
          {testCases.map((testCase, index) => (
            <div key={index}>
              <Button 
                onClick={() => {
                  setValue(testCase.value);
                  setFormat(testCase.format);
                }}
                style={{ marginBottom: '10px' }}
              >
                {testCase.name}
              </Button>
              <div style={{ 
                border: '1px solid #d9d9d9', 
                padding: '10px', 
                backgroundColor: '#fafafa',
                marginBottom: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '80px'
              }}>
                <BarcodeRenderer
                  value={testCase.value}
                  format={testCase.format}
                  width={1.5}
                  height={60}
                  displayValue={true}
                  fontSize={12}
                  style={{
                    maxWidth: '300px',
                    maxHeight: '80px'
                  }}
                />
              </div>
            </div>
          ))}
        </Space>
      </Card>
    </div>
  );
};

export default BarcodeTest;
