/**
 * 条形码识别服务
 * 使用ZXing库从base64图片中识别条形码内容
 */

import { BrowserMultiFormatReader } from '@zxing/browser';

class BarcodeReader {
  constructor() {
    this.reader = new BrowserMultiFormatReader();
  }

  /**
   * 从base64图片中识别条形码内容
   * @param {string} base64Image - base64格式的图片数据
   * @returns {Promise<string|null>} 识别到的条形码内容，失败返回null
   */
  async readFromBase64(base64Image) {
    try {
      // 确保base64数据格式正确
      let imageData = base64Image;
      if (!imageData.startsWith('data:image/')) {
        imageData = `data:image/png;base64,${base64Image}`;
      }

      // 创建Image对象
      const img = new Image();
      
      return new Promise((resolve) => {
        img.onload = async () => {
          try {
            // 创建Canvas来处理图片
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            // 使用ZXing识别条形码
            const result = await this.reader.decodeFromCanvas(canvas);
            
            if (result && result.text) {
              console.log(`条形码识别成功: ${result.text}, 格式: ${result.format}`);
              resolve(result.text);
            } else {
              console.warn('未能识别出条形码内容');
              resolve(null);
            }
          } catch (error) {
            console.error('条形码识别失败:', error);
            resolve(null);
          }
        };

        img.onerror = () => {
          console.error('图片加载失败');
          resolve(null);
        };

        img.src = imageData;
      });
    } catch (error) {
      console.error('条形码识别过程出错:', error);
      return null;
    }
  }

  /**
   * 从图片URL中识别条形码内容
   * @param {string} imageUrl - 图片URL
   * @returns {Promise<string|null>} 识别到的条形码内容，失败返回null
   */
  async readFromUrl(imageUrl) {
    try {
      const result = await this.reader.decodeFromImageUrl(imageUrl);
      
      if (result && result.text) {
        console.log(`条形码识别成功: ${result.text}, 格式: ${result.format}`);
        return result.text;
      } else {
        console.warn('未能识别出条形码内容');
        return null;
      }
    } catch (error) {
      console.error('条形码识别失败:', error);
      return null;
    }
  }

  /**
   * 从Canvas元素中识别条形码内容
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @returns {Promise<string|null>} 识别到的条形码内容，失败返回null
   */
  async readFromCanvas(canvas) {
    try {
      const result = await this.reader.decodeFromCanvas(canvas);
      
      if (result && result.text) {
        console.log(`条形码识别成功: ${result.text}, 格式: ${result.format}`);
        return result.text;
      } else {
        console.warn('未能识别出条形码内容');
        return null;
      }
    } catch (error) {
      console.error('条形码识别失败:', error);
      return null;
    }
  }

  /**
   * 批量识别多个base64图片中的条形码
   * @param {string[]} base64Images - base64图片数组
   * @returns {Promise<Array<{index: number, content: string|null}>>} 识别结果数组
   */
  async readMultipleFromBase64(base64Images) {
    const results = [];
    
    for (let i = 0; i < base64Images.length; i++) {
      const content = await this.readFromBase64(base64Images[i]);
      results.push({
        index: i,
        content: content
      });
    }
    
    return results;
  }

  /**
   * 释放资源
   */
  destroy() {
    if (this.reader) {
      this.reader.reset();
    }
  }
}

// 创建单例实例
const barcodeReader = new BarcodeReader();

export default barcodeReader;
