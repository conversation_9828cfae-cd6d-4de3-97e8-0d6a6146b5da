/**
 * 大模型数据转换器
 * 将大模型返回的数据转换为标准的XPrinter格式
 */

class LLMDataConverter {
  constructor() {
    this.canvasWidth = 800;
    this.canvasHeight = 600;
  }

  /**
   * 转换大模型数据为XPrinter格式
   * @param {Object} llmData - 大模型返回的数据
   * @returns {Array} XPrinter格式的数据数组
   */
  convertToXPrinter(llmData) {
    try {
      console.log('开始转换大模型数据:', llmData);
      
      if (!llmData.result || !llmData.result.pages) {
        throw new Error('大模型数据格式不正确');
      }

      const result = [];
      const pages = llmData.result.pages;

      // 处理每一页
      pages.forEach((page, pageIndex) => {
        console.log(`处理第${pageIndex + 1}页:`, page);
        
        // 设置画布尺寸
        if (page.width && page.height) {
          this.canvasWidth = page.width;
          this.canvasHeight = page.height;
        }

        // 处理页面元素
        if (page.elements && Array.isArray(page.elements)) {
          page.elements.forEach(element => {
            const convertedElement = this.convertElement(element);
            if (convertedElement) {
              result.push(convertedElement);
            }
          });
        }
      });

      console.log('大模型数据转换完成，结果:', result);
      return result;
    } catch (error) {
      console.error('大模型数据转换失败:', error);
      throw new Error(`大模型数据转换失败: ${error.message}`);
    }
  }

  /**
   * 转换单个元素
   * @param {Object} element - 大模型返回的元素
   * @returns {Object} 转换后的XPrinter元素
   */
  convertElement(element) {
    // 确保必需的字段存在
    const convertedElement = {
      elementType: element.elementType || 1,
      x: this.ensureString(element.x || '0'),
      y: this.ensureString(element.y || '0'),
      width: this.ensureString(element.width || '100'),
      height: this.ensureString(element.height || '20'),
      rotationAngle: this.ensureString(element.rotationAngle || '0'),
      lockLocation: this.ensureString(element.lockLocation || 'false'),
      takePrint: this.ensureString(element.takePrint || 'true'),
      mirrorImage: this.ensureString(element.mirrorImage || 'false')
    };

    // 根据元素类型添加特定属性
    switch (String(element.elementType)) {
      case '1': // 文本
        return this.convertTextElement(element, convertedElement);
      case '2': // 条形码
        return this.convertBarcodeElement(element, convertedElement);
      case '3': // 画布
        return this.convertCanvasElement(element, convertedElement);
      case '6': // 图片
        return this.convertImageElement(element, convertedElement);
      case '7': // 二维码
        return this.convertQRCodeElement(element, convertedElement);
      default:
        console.warn('未知的元素类型:', element.elementType);
        return convertedElement;
    }
  }

  /**
   * 转换文本元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性
   * @returns {Object} 转换后的文本元素
   */
  convertTextElement(element, base) {
    return {
      ...base,
      content: element.content || '',
      textSize: this.ensureString(element.textSize || '12.0'),
      hAlignment: this.ensureString(element.hAlignment || '1'),
      bold: this.ensureString(element.bold || 'false'),
      italic: this.ensureString(element.italic || 'false'),
      underline: this.ensureString(element.underline || 'false'),
      strikethrough: this.ensureString(element.strikethrough || 'false'),
      wordSpace: this.ensureString(element.wordSpace || '0.0'),
      linesSpace: this.ensureString(element.linesSpace || '0.0'),
      fontType: this.ensureString(element.fontType || '-2'),
      blackWhiteReflection: this.ensureString(element.blackWhiteReflection || 'false'),
      automaticHeightCalculation: this.ensureString(element.automaticHeightCalculation || 'true'),
      lineWrap: this.ensureString(element.lineWrap || 'true'),
      flipX: this.ensureString(element.flipX || 'false'),
      // XPrinter必需字段
      inputDataType: this.ensureString(element.inputDataType || '1'),
      transmutationType: element.transmutationType || 1,
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      controlType: this.ensureString(element.controlType || '3')
    };
  }

  /**
   * 转换条形码元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性
   * @returns {Object} 转换后的条形码元素
   */
  convertBarcodeElement(element, base) {
    return {
      ...base,
      content: element.content || '',
      barcodeType: this.ensureString(element.barcodeType || '1'),
      showText: this.ensureString(element.showText || '3'),
      textAlignment: element.textAlignment || 1,
      horizontalAlignment: this.ensureString(element.horizontalAlignment || 'true'),
      inputDataType: this.ensureString(element.inputDataType || '1'),
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationType: element.transmutationType || 1,
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      showKeyName: element.showKeyName || false,
      textSize: this.ensureString(element.textSize || '11.0'),
      fontType: this.ensureString(element.fontType || '-2'),
      bold: this.ensureString(element.bold || 'false'),
      italic: this.ensureString(element.italic || 'false'),
      underline: this.ensureString(element.underline || 'false'),
      strikethrough: this.ensureString(element.strikethrough || 'false'),
      controlType: this.ensureString(element.controlType || '2')
    };
  }

  /**
   * 转换画布元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性
   * @returns {Object} 转换后的画布元素
   */
  convertCanvasElement(element, base) {
    return {
      elementType: 3,
      os: element.os || 'web',
      versionCode: element.versionCode || 0,
      cableLabelDirection: element.cableLabelDirection || 2,
      cableLabelLength: element.cableLabelLength || 0,
      width: this.ensureString(element.width || this.canvasWidth.toString()),
      height: this.ensureString(element.height || this.canvasHeight.toString()),
      dpi: element.dpi || 144,
      labelType: element.labelType || 1,
      blackMarkHeight: element.blackMarkHeight || 0,
      blackMarkOffset: element.blackMarkOffset || 0,
      blackMarkDirection: element.blackMarkDirection || 0
    };
  }

  /**
   * 转换图片元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性
   * @returns {Object} 转换后的图片元素
   */
  convertImageElement(element, base) {
    return {
      ...base,
      content: element.content || '',
      colorMode: this.ensureString(element.colorMode || '0'),
      grayValue: this.ensureString(element.grayValue || '128'),
      tile: this.ensureString(element.tile || 'false'),
      blackWhiteReflection: this.ensureString(element.blackWhiteReflection || 'false')
    };
  }

  /**
   * 转换二维码元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性
   * @returns {Object} 转换后的二维码元素
   */
  convertQRCodeElement(element, base) {
    return {
      ...base,
      content: element.content || '',
      codeType: element.codeType || 'QR_CODE',
      whiteMargin: this.ensureString(element.whiteMargin || '0'),
      errorCorrectionLevel: element.errorCorrectionLevel || 'M',
      inputDataType: this.ensureString(element.inputDataType || '1'),
      prefix: element.prefix || '',
      suffix: element.suffix || '',
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationType: element.transmutationType || 1,
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      showKeyName: element.showKeyName || false
    };
  }

  /**
   * 确保值为字符串类型
   * @param {any} value - 输入值
   * @returns {string} 字符串值
   */
  ensureString(value) {
    return String(value);
  }
}

// 创建单例实例
const llmDataConverter = new LLMDataConverter();

export default llmDataConverter;
