/**
 * 大模型视觉API服务
 * 支持多种大模型提供商进行图像识别和XPrinter数据生成
 */

class LLMVisionApi {
  constructor() {
    this.providers = {
      deepseek: {
        name: 'DeepSeek',
        baseUrl: 'https://api.deepseek.com',
        model: 'deepseek-vl-67b-chat'
      },
      openai: {
        name: 'OpenAI GPT-4V',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-4-vision-preview'
      },
      claude: {
        name: 'Claude 3 Vision',
        baseUrl: 'https://api.anthropic.com/v1',
        model: 'claude-3-sonnet-20240229'
      }
    };
    
    this.currentProvider = 'deepseek';
    this.apiKey = '';
  }

  /**
   * 设置API配置
   * @param {string} provider - 提供商名称
   * @param {string} apiKey - API密钥
   */
  setConfig(provider, apiKey) {
    if (!this.providers[provider]) {
      throw new Error(`不支持的提供商: ${provider}`);
    }
    this.currentProvider = provider;
    this.apiKey = apiKey;
  }

  /**
   * 获取XPrinter格式的提示词模板
   * @returns {string} 提示词
   */
  getXPrinterPrompt() {
    return `请分析这张图片，识别其中的所有文本、条形码、二维码等元素，并返回XPrinter格式的JSON数据。

要求：
1. 准确识别每个元素的位置坐标(x, y)和尺寸(width, height)
2. 识别文本的样式属性：字体大小(textSize)、粗体(bold)、斜体(italic)、下划线(underline)等
3. 识别条形码和二维码的内容
4. 按照XPrinter数据格式返回JSON数组

XPrinter元素类型：
- elementType: 1 = 文本
- elementType: 2 = 条形码  
- elementType: 3 = 画布
- elementType: 6 = 图片
- elementType: 7 = 二维码

条形码类型(barcodeType)：
- 1 = CODE_128
- 2 = CODE_39
- 4 = EAN_13
- 5 = EAN_8
- 8 = UPC_A

文本对齐(hAlignment)：
- 1 = 左对齐
- 2 = 居中对齐  
- 3 = 右对齐

示例JSON格式：
[
  {
    "elementType": 3,
    "width": "800",
    "height": "600",
    "os": "web",
    "versionCode": 0
  },
  {
    "elementType": 1,
    "x": "50",
    "y": "20", 
    "width": "200",
    "height": "30",
    "content": "产品名称",
    "textSize": "16.0",
    "bold": "true",
    "italic": "false",
    "underline": "false",
    "hAlignment": "1",
    "rotationAngle": "0"
  },
  {
    "elementType": 2,
    "x": "50",
    "y": "100",
    "width": "300", 
    "height": "80",
    "content": "1234567890123",
    "barcodeType": "4",
    "showText": "3",
    "textAlignment": 1
  }
]

请仔细分析图片中的每个元素，准确估算位置和尺寸，识别文本样式，只返回JSON数据，不要包含其他说明文字。`;
  }

  /**
   * 调用大模型API进行图像识别
   * @param {string} base64Image - base64格式的图片
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeImage(base64Image) {
    if (!this.apiKey) {
      throw new Error('请先配置API密钥');
    }

    const provider = this.providers[this.currentProvider];
    
    try {
      console.log(`使用 ${provider.name} 进行图像识别...`);
      
      let response;
      
      switch (this.currentProvider) {
        case 'deepseek':
          response = await this.callDeepSeekAPI(base64Image);
          break;
        case 'openai':
          response = await this.callOpenAIAPI(base64Image);
          break;
        case 'claude':
          response = await this.callClaudeAPI(base64Image);
          break;
        default:
          throw new Error(`未实现的提供商: ${this.currentProvider}`);
      }

      return this.parseResponse(response);
    } catch (error) {
      console.error('大模型API调用失败:', error);
      throw new Error(`${provider.name} API调用失败: ${error.message}`);
    }
  }

  /**
   * 调用DeepSeek API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callDeepSeekAPI(base64Image) {
    // 确保图片格式正确
    let imageUrl = base64Image;
    if (!imageUrl.startsWith('data:')) {
      imageUrl = `data:image/jpeg;base64,${base64Image}`;
    }

    const requestBody = {
      model: this.providers.deepseek.model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: this.getXPrinterPrompt()
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1,
      stream: false
    };

    console.log('DeepSeek API 请求:', {
      url: `${this.providers.deepseek.baseUrl}/chat/completions`,
      model: this.providers.deepseek.model,
      imageSize: imageUrl.length
    });

    const response = await fetch(`${this.providers.deepseek.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('DeepSeek API 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API 错误响应:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: { message: errorText } };
      }

      throw new Error(`HTTP ${response.status}: ${errorData.error?.message || errorData.message || response.statusText}`);
    }

    const result = await response.json();
    console.log('DeepSeek API 响应:', result);
    return result;
  }

  /**
   * 调用OpenAI API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callOpenAIAPI(base64Image) {
    const response = await fetch(`${this.providers.openai.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: this.providers.openai.model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: this.getXPrinterPrompt()
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image.startsWith('data:') ? base64Image : `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 调用Claude API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callClaudeAPI(base64Image) {
    // Claude API的实现
    throw new Error('Claude API暂未实现，请使用DeepSeek或OpenAI');
  }

  /**
   * 解析API响应
   * @param {Object} response - API响应
   * @returns {Object} 解析后的结果
   */
  parseResponse(response) {
    try {
      let content = '';
      
      if (response.choices && response.choices[0]) {
        content = response.choices[0].message?.content || '';
      }

      // 提取JSON部分
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('响应中未找到有效的JSON数据');
      }

      const jsonData = JSON.parse(jsonMatch[0]);
      
      return {
        result: {
          pages: [{
            width: 800,
            height: 600,
            elements: jsonData
          }]
        }
      };
    } catch (error) {
      console.error('解析响应失败:', error);
      throw new Error(`解析响应失败: ${error.message}`);
    }
  }

  /**
   * 获取支持的提供商列表
   * @returns {Array} 提供商列表
   */
  getProviders() {
    return Object.entries(this.providers).map(([key, value]) => ({
      key,
      name: value.name,
      model: value.model
    }));
  }
}

// 创建单例实例
const llmVisionApi = new LLMVisionApi();

export default llmVisionApi;
